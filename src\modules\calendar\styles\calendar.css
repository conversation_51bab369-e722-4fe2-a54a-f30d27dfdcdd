/* <PERSON><PERSON><PERSON> b<PERSON><PERSON> FullCalendar styles đ<PERSON><PERSON><PERSON> tải */
@import url('https://cdn.jsdelivr.net/npm/fullcalendar@5.11.3/main.min.css');

/* Fix z-index for DatePicker in EventForm Modal */
.event-form-datepicker .datepicker-dropdown {
  z-index: 9999 !important;
}

/* Global fix for DatePicker dropdown in modals */
.datepicker-dropdown {
  z-index: 9999 !important;
  position: fixed !important;
}

/* Ensure DatePicker dropdown appears above modal overlay */
[data-floating-ui-portal] .datepicker-dropdown {
  z-index: 9999 !important;
  position: fixed !important;
}

/* Fix DatePicker positioning in modals */
.modal .datepicker-dropdown {
  z-index: 9999 !important;
  position: fixed !important;
}

/* Prevent modal from interfering with DatePicker positioning */
.modal {
  position: relative;
  z-index: 9500;
}

.modal .event-form-datepicker {
  position: relative;
  z-index: auto;
}

/* Calendar container with beautiful animations */
.calendar-container {
  width: 100% !important;
  height: 100%;
  font-family: inherit;
  border-radius: var(--radius-md);
  overflow: visible !important;
  box-shadow: var(--shadow-lg);
  background: linear-gradient(135deg, var(--fc-page-bg-color) 0%, rgba(59, 130, 246, 0.02) 100%);
  transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
  display: flex;
  flex-direction: column;
  position: relative;
  animation: slideInUp 0.6s ease-out;
  margin: 0 !important;
  padding: 0 !important;
  box-sizing: border-box;
}

.calendar-container::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 3px;
  background: linear-gradient(90deg, #3b82f6, #8b5cf6, #06b6d4, #10b981);
  background-size: 300% 100%;
  animation: gradientShift 3s ease-in-out infinite;
}

.calendar-container:hover {
  box-shadow: var(--shadow-xl);
  transform: translateY(-2px);
}

@keyframes slideInUp {
  from {
    opacity: 0;
    transform: translateY(30px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes gradientShift {
  0%,
  100% {
    background-position: 0% 50%;
  }
  50% {
    background-position: 100% 50%;
  }
}

/* Custom header for calendar */
.calendar-custom-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 0.75rem 1.25rem;
  background-color: var(--fc-page-bg-color);
  z-index: 10;
  width: 100% !important;
  box-sizing: border-box;
  margin: 0 !important;
}

.calendar-custom-header-left,
.calendar-custom-header-right {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  flex-shrink: 0;
  min-width: 0;
}

.calendar-custom-header-center {
  flex: 1;
  text-align: center;
  min-width: 0;
  overflow: hidden;
}

.calendar-title {
  font-size: 1.25rem;
  font-weight: 600;
  color: var(--color-foreground);
  margin: 0;
  padding: 0;
  line-height: 1.2;
  letter-spacing: -0.01em;
}

/* Custom navigation buttons with beautiful effects */
.calendar-nav-button {
  width: 2.125rem;
  height: 2.125rem;
  padding: 0;
  display: flex;
  align-items: center;
  justify-content: center;
  background: linear-gradient(135deg, var(--color-card) 0%, rgba(59, 130, 246, 0.02) 100%);
  color: var(--color-foreground);
  border: 1px solid var(--color-border);
  border-radius: var(--radius-md);
  box-shadow: var(--shadow-sm);
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  position: relative;
  overflow: hidden;
  cursor: pointer;
}

.calendar-nav-button::before {
  content: '';
  position: absolute;
  top: 50%;
  left: 50%;
  width: 0;
  height: 0;
  background: radial-gradient(circle, rgba(59, 130, 246, 0.2) 0%, transparent 70%);
  transition: all 0.3s ease;
  transform: translate(-50%, -50%);
}

.calendar-nav-button:hover {
  background: linear-gradient(135deg, var(--color-card-hover) 0%, rgba(59, 130, 246, 0.05) 100%);
  color: var(--color-primary);
  border-color: var(--color-primary);
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(59, 130, 246, 0.2);
}

.calendar-nav-button:hover::before {
  width: 100%;
  height: 100%;
}

.calendar-nav-button:active {
  transform: translateY(0);
  box-shadow: var(--shadow-sm);
}

.calendar-nav-icon {
  font-size: 0.9375rem;
  position: relative;
  z-index: 1;
}

/* Custom today button */
.calendar-today-button {
  background-color: var(--color-card);
  border: 1px solid var(--color-border);
  color: var(--color-foreground);
  font-weight: 500;
  padding: 0.35rem 0.7rem;
  position: relative;
  overflow: hidden;
  box-shadow: var(--shadow-sm);
  transition: all 0.3s ease;
  font-size: 0.8125rem;
  height: 2.125rem;
  border-radius: var(--radius-md);
  cursor: pointer;
}

.calendar-today-button:hover {
  background-color: var(--color-card-hover);
  box-shadow: none;
  transform: translateY(0);
  border-color: var(--color-primary);
  color: var(--color-primary);
}

.calendar-today-button:active {
  transform: translateY(0);
  box-shadow: none;
  background-color: var(--color-card);
}

/* Add event button */
.calendar-add-event-button {
  display: flex;
  align-items: center;
  background-color: var(--color-primary);
  color: var(--color-primary-foreground);
  border: none;
  border-radius: var(--radius-md);
  padding: 0.35rem 0.7rem;
  font-size: 0.8125rem;
  font-weight: 500;
  height: 2.125rem;
  cursor: pointer;
  box-shadow: var(--shadow-sm);
  transition: all 0.3s ease;
  margin-left: 0.5rem;
}

.calendar-add-event-button:hover {
  background-color: var(--color-primary-600);
  transform: translateY(-2px);
  box-shadow: var(--shadow-md);
}

.calendar-add-event-button:active {
  transform: translateY(0);
  box-shadow: var(--shadow-sm);
  background-color: var(--color-primary-700);
}

.calendar-add-event-icon {
  font-size: 1.1rem;
  margin-right: 0.4rem;
  font-weight: 600;
  line-height: 1;
}

/* Đảm bảo FullCalendar chiếm toàn bộ chiều rộng */
.calendar-container .fc {
  width: 100% !important;
  max-width: 100% !important;
  flex: 1;
}

/* Force full width for all calendar elements */
.fc,
.fc-view,
.fc-view-container,
.fc-view-harness,
.fc-view-harness-active {
  width: 100% !important;
  max-width: 100% !important;
  margin: 0 !important;
  padding: 0 !important;
}

/* Remove any margins or padding that might cause gaps */
.fc-scrollgrid,
.fc-scrollgrid-section,
.fc-scrollgrid-section-header,
.fc-scrollgrid-section-body {
  width: 100% !important;
  margin: 0 !important;
  padding: 0 !important;
}

/* Base calendar styles - áp dụng cho cả light và dark mode */
:root {
  /* Sử dụng các biến từ hệ thống theme */
  --fc-border-color: var(--color-border);
  --fc-page-bg-color: var(--color-card);
  --fc-neutral-bg-color: var(--color-background);
  --fc-neutral-text-color: var(--color-foreground);
  --fc-button-text-color: var(--color-primary-foreground);
  --fc-button-bg-color: var(--color-primary);
  --fc-button-border-color: var(--color-primary);
  --fc-button-hover-bg-color: var(--color-primary-600);
  --fc-button-hover-border-color: var(--color-primary-600);
  --fc-button-active-bg-color: var(--color-primary-700);
  --fc-button-active-border-color: var(--color-primary-700);
  --fc-event-bg-color: var(--color-primary);
  --fc-event-border-color: var(--color-primary);
  --fc-event-text-color: var(--color-primary-foreground);
  --fc-event-selected-overlay-color: rgba(0, 0, 0, 0.15);
  --fc-more-link-bg-color: var(--color-card-muted);
  --fc-more-link-text-color: var(--color-foreground);
  --fc-today-bg-color: rgba(var(--color-primary-rgb, 255, 51, 51), 0.05);
  --fc-now-indicator-color: var(--color-primary);
  --fc-highlight-color: rgba(var(--color-primary-rgb, 255, 51, 51), 0.03);
  --fc-week-number-bg-color: var(--color-card-muted);
  --fc-week-number-color: var(--color-muted);
}

/* Dark mode styles - sử dụng biến màu từ hệ thống theme */
.fc-theme-dark {
  --fc-border-color: var(--color-border);
  --fc-page-bg-color: var(--color-card);
  --fc-neutral-bg-color: var(--color-background);
  --fc-neutral-text-color: var(--color-foreground);
  --fc-event-selected-overlay-color: rgba(255, 255, 255, 0.25);
  --fc-today-bg-color: rgba(255, 51, 51, 0.08);
  --fc-highlight-color: rgba(255, 51, 51, 0.05);
  --fc-non-business-color: rgba(0, 0, 0, 0.1);
}

/* Light mode styles - sử dụng biến màu từ hệ thống theme */
.fc-theme-light {
  --fc-border-color: var(--color-border);
  --fc-page-bg-color: var(--color-card);
  --fc-neutral-bg-color: var(--color-background);
  --fc-neutral-text-color: var(--color-foreground);
  --fc-event-selected-overlay-color: rgba(0, 0, 0, 0.15);
  --fc-today-bg-color: rgba(255, 51, 51, 0.05);
  --fc-highlight-color: rgba(255, 51, 51, 0.03);
  --fc-non-business-color: rgba(0, 0, 0, 0.05);
}

/* Calendar header */
.fc .fc-toolbar {
  margin-bottom: 1rem;
  padding: 1rem 1.25rem;
  background-color: var(--fc-page-bg-color);
  display: flex;
  align-items: center;
  flex-wrap: wrap;
  gap: 0.75rem;
}

/* ViewSelect button styling */
.calendar-view-select-button {
  background-color: var(--color-primary) !important;
  color: var(--color-primary-foreground) !important;
  border: none !important;
}

.calendar-view-select-button:hover {
  background-color: var(--color-primary) !important;
  opacity: 0.9;
}

/* ViewSelect dropdown styling */
.calendar-view-select .select-trigger {
  background-color: var(--color-primary) !important;
  color: var(--color-primary-foreground) !important;
  border: none !important;
}

/* Override Select component styling for calendar view select */
.calendar-view-select .relative > div:first-child {
  background-color: var(--color-primary) !important;
  color: var(--color-primary-foreground) !important;
  border: none !important;
}

.calendar-view-select .relative > div:first-child:hover {
  background-color: var(--color-primary) !important;
  opacity: 0.9;
}

.fc .fc-toolbar-chunk {
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.fc .fc-toolbar-title {
  font-size: 1.25rem;
  font-weight: 600;
  color: var(--fc-neutral-text-color);
  margin: 0;
  padding: 0;
  line-height: 1.2;
  letter-spacing: -0.01em;
}

/* Calendar buttons */
.fc .fc-button {
  font-weight: 500;
  font-size: 0.8125rem;
  padding: 0.35rem 0.7rem;
  border-radius: var(--radius-md);
  transition: all 0.2s ease;
  box-shadow: var(--shadow-sm);
  border: 1px solid transparent;
  height: 2.125rem;
  display: inline-flex;
  align-items: center;
  justify-content: center;
  letter-spacing: 0.01em;
}

.fc .fc-button:focus {
  box-shadow: 0 0 0 2px var(--color-primary-muted);
  outline: none;
}

.fc .fc-button:hover {
  opacity: 0.95;
  transform: translateY(-1px);
  box-shadow: var(--shadow-md);
}

.fc .fc-button:active {
  transform: translateY(0);
  box-shadow: var(--shadow-sm);
}

/* Primary buttons (today) */
.fc .fc-today-button {
  background-color: var(--color-primary);
  border: none !important;
  color: var(--color-primary-foreground);
  font-weight: 500;
  padding: 0.35rem 0.7rem 0.35rem 2.1rem !important;
  position: relative;
  overflow: hidden;
  box-shadow: var(--shadow-sm);
  transition: all 0.3s ease;
  font-size: 0.8125rem;
  height: 2.125rem;
  border-radius: var(--radius-md);
}

.fc .fc-today-button::before {
  content: '📆';
  font-family: 'Segoe UI Symbol', sans-serif;
  position: absolute;
  left: 0.6rem;
  top: 50%;
  transform: translateY(-50%);
  font-size: 0.875rem;
}

.fc .fc-today-button:hover {
  background-color: var(--color-primary-600);
  box-shadow: var(--shadow-md);
  transform: translateY(-2px);
}

.fc .fc-today-button:active {
  transform: translateY(0);
  box-shadow: var(--shadow-sm);
  background-color: var(--color-primary-700);
}

.fc .fc-button-primary:not(:disabled).fc-button-active,
.fc .fc-button-primary:not(:disabled):active {
  background-color: var(--color-primary-700);
  border: none;
  box-shadow: inset 0 1px 3px rgba(0, 0, 0, 0.2);
}

/* View buttons (month, week, day, list) */
.fc .fc-button-group {
  display: flex;
  gap: 0.5rem;
  background: transparent;
  padding: 0;
  border-radius: 0;
  box-shadow: none;
  border: none;
}

.fc .fc-button-group .fc-button {
  border-radius: var(--radius-md);
  box-shadow: var(--shadow-sm);
  border: none !important;
  margin: 0 !important;
  padding: 0.35rem 0.6rem;
  background-color: var(--color-primary);
  color: var(--color-primary-foreground);
  position: relative;
  overflow: hidden;
  transition: all 0.2s ease;
  font-weight: 500;
  min-width: 4.25rem;
  font-size: 0.8rem;
  height: 2rem;
  z-index: 1;
}

.fc .fc-button-group .fc-button:hover {
  z-index: 2;
}

.fc .fc-button-group .fc-button::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: var(--color-primary);
  border-radius: var(--radius-md);
  opacity: 0;
  transform: scale(0.8);
  transition: all 0.3s cubic-bezier(0.175, 0.885, 0.32, 1.275);
  z-index: -1;
}

.fc .fc-button-group .fc-button:hover {
  background-color: var(--color-primary-600);
  transform: translateY(-2px);
  box-shadow: var(--shadow-md);
}

.fc .fc-button-group .fc-button:hover::before {
  opacity: 0.1;
}

.fc .fc-button-group .fc-button-active {
  background-color: var(--color-primary-700);
  color: var(--color-primary-foreground);
  font-weight: 600;
  box-shadow: var(--shadow-md);
  transform: translateY(0) !important;
  z-index: 3;
}

/* Add icons to view buttons */
.fc .fc-dayGridMonth-button::before,
.fc .fc-timeGridWeek-button::before,
.fc .fc-timeGridDay-button::before,
.fc .fc-listWeek-button::before {
  font-family: 'Segoe UI Symbol', sans-serif;
  margin-right: 0.3rem;
  font-size: 0.85rem;
  display: inline-block;
  vertical-align: middle;
  line-height: 1;
}

.fc .fc-dayGridMonth-button::before {
  content: '📅';
}

.fc .fc-timeGridWeek-button::before {
  content: '📊';
}

.fc .fc-timeGridDay-button::before {
  content: '📋';
}

.fc .fc-listWeek-button::before {
  content: '📝';
}

/* Navigation buttons (prev, next) */
.fc .fc-prev-button,
.fc .fc-next-button {
  width: 2.125rem;
  height: 2.125rem;
  padding: 0;
  display: flex;
  align-items: center;
  justify-content: center;
  background-color: var(--color-primary);
  color: var(--color-primary-foreground);
  border: none !important;
  border-radius: var(--radius-md) !important;
  box-shadow: var(--shadow-sm);
  transition: all 0.3s ease;
  position: relative;
  overflow: hidden;
}

.fc .fc-prev-button::before,
.fc .fc-next-button::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: var(--color-primary);
  opacity: 0;
  transition: opacity 0.3s ease;
}

.fc .fc-prev-button:hover,
.fc .fc-next-button:hover {
  background-color: var(--color-primary-600);
  color: var(--color-primary-foreground);
  transform: translateY(-2px);
  box-shadow: var(--shadow-md);
}

.fc .fc-prev-button:hover::before,
.fc .fc-next-button:hover::before {
  opacity: 0.1;
}

.fc .fc-prev-button:active,
.fc .fc-next-button:active {
  transform: translateY(0);
  box-shadow: var(--shadow-sm);
  background-color: var(--color-primary-700);
}

.fc .fc-prev-button .fc-icon,
.fc .fc-next-button .fc-icon {
  font-size: 0.9375rem;
  position: relative;
  z-index: 1;
}

/* Event styles with beautiful animations */
.fc-event {
}

.fc-event::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.3), transparent);
  transition: left 0.6s ease;
}

.fc-event:hover {
  transform: translateY(-2px) scale(1.02) !important;
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15) !important;
  z-index: 5;
}

.fc-event:hover::before {
  left: 100%;
}

@keyframes eventSlideIn {
  from {
    opacity: 0;
    transform: translateX(-10px);
  }
  to {
    opacity: 1;
    transform: translateX(0);
  }
}

.fc-event-main {
  padding: 0 !important;
  overflow: hidden !important;
  width: 100% !important;
  display: flex !important;
  flex-direction: column !important;
  justify-content: center !important;
}

.fc-event-time {
  font-weight: 600;
  font-size: 0.65rem;
  letter-spacing: 0.02em;
  opacity: 0.85;
  line-height: 1.1;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  margin-bottom: 1px;
}

.fc-event-title {
  font-weight: 500;
  font-size: 0.7rem;
  line-height: 1.1;
  display: block;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  max-width: 100%;
}

/* Today highlight with beautiful glow effect */
.fc .fc-day-today {
  background: linear-gradient(
    135deg,
    var(--fc-today-bg-color) 0%,
    rgba(59, 130, 246, 0.1) 100%
  ) !important;
  position: relative;
  animation: todayGlow 2s ease-in-out infinite alternate;
}

.fc .fc-day-today::after {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: radial-gradient(circle at center, rgba(59, 130, 246, 0.1) 0%, transparent 70%);
  pointer-events: none;
  animation: todayPulse 3s ease-in-out infinite;
}

@keyframes todayGlow {
  from {
    box-shadow: 0 0 5px rgba(59, 130, 246, 0.3);
  }
  to {
    box-shadow: 0 0 20px rgba(59, 130, 246, 0.5);
  }
}

@keyframes todayPulse {
  0%,
  100% {
    opacity: 0.5;
    transform: scale(1);
  }
  50% {
    opacity: 0.8;
    transform: scale(1.05);
  }
}

/* Day header with beautiful animations */
.fc .fc-col-header-cell {
  padding: 0.75rem 0;
  font-weight: 600;
  background: linear-gradient(135deg, var(--color-card) 0%, rgba(59, 130, 246, 0.03) 100%);
  border-bottom: 1px solid var(--color-border);
  text-transform: uppercase;
  letter-spacing: 0.05em;
  position: relative;
  transition: all 0.3s ease;
}

.fc .fc-col-header-cell::after {
  content: '';
  position: absolute;
  bottom: 0;
  left: 50%;
  width: 0;
  height: 2px;
  background: linear-gradient(90deg, #3b82f6, #8b5cf6);
  transition: all 0.3s ease;
  transform: translateX(-50%);
}

.fc .fc-col-header-cell:hover::after {
  width: 80%;
}

.fc .fc-col-header-cell-cushion {
  color: var(--color-muted);
  text-decoration: none !important;
  padding: 0.5rem;
  font-size: 0.8rem;
  font-weight: 500;
  transition: all 0.3s ease;
}

.fc .fc-col-header-cell:hover .fc-col-header-cell-cushion {
  color: var(--color-primary);
  transform: translateY(-1px);
}

/* Time grid */
.fc .fc-timegrid-slot {
  height: 3.5rem;
}

.fc .fc-timegrid-axis {
  background-color: var(--fc-neutral-bg-color);
}

.fc .fc-timegrid-slot-label {
  color: var(--color-muted);
  font-weight: 500;
  font-size: 0.75rem;
}

/* List view */
.fc-list-day-cushion {
  padding: 1rem !important;
  background-color: var(--fc-neutral-bg-color) !important;
  color: var(--color-muted) !important;
  font-weight: 500;
  width: 100% !important;
}

.fc-list-event:hover td {
  background-color: var(--fc-highlight-color) !important;
}

/* List view date headers */
.fc .fc-list-day-text {
  color: var(--color-muted);
  font-weight: 500;
  text-align: left;
}

.fc .fc-list-day-side-text {
  color: var(--color-muted);
  font-weight: 400;
}

/* Daygrid view */
.fc .fc-daygrid-day-number {
  padding: 0.5rem;
  color: var(--color-muted);
  text-decoration: none !important;
  font-weight: 500;
  width: 2rem;
  height: 2rem;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 50%;
  margin: 0.25rem;
  transition: all 0.2s ease;
}

.fc .fc-daygrid-day-number:hover {
  background-color: var(--color-card-muted);
  color: var(--color-foreground);
}

.fc-day-today .fc-daygrid-day-number {
  background-color: var(--color-primary);
  color: var(--color-primary-foreground);
  font-weight: 600;
}

.fc .fc-daygrid-day-top {
  justify-content: center;
  padding-top: 0.25rem;
}

/* Other month dates (previous/next month) */
.fc .fc-day-other .fc-daygrid-day-number {
  color: var(--color-muted);
  opacity: 0.5;
  font-weight: 400;
}

.fc .fc-day-other .fc-daygrid-day-number:hover {
  background-color: var(--color-card-muted);
  color: var(--color-muted);
  opacity: 0.7;
}

/* Weekend styling */
.fc .fc-day-sat .fc-daygrid-day-number,
.fc .fc-day-sun .fc-daygrid-day-number {
  color: var(--color-muted);
  font-weight: 500;
}

.fc .fc-day-sat .fc-col-header-cell-cushion,
.fc .fc-day-sun .fc-col-header-cell-cushion {
  color: var(--color-muted);
  opacity: 0.8;
}

/* Event colors - thêm màu sắc đa dạng cho các loại sự kiện */
.calendar-event-meeting {
  background-color: var(--color-primary) !important;
  border-color: var(--color-primary) !important;
  color: var(--color-primary-foreground) !important;
}

.calendar-event-appointment {
  background-color: var(--color-info) !important;
  border-color: var(--color-info) !important;
  color: var(--color-info-foreground) !important;
}

.calendar-event-deadline {
  background-color: var(--color-error) !important;
  border-color: var(--color-error) !important;
  color: var(--color-error-foreground) !important;
}

.calendar-event-lunch {
  background-color: var(--color-success) !important;
  border-color: var(--color-success) !important;
  color: var(--color-success-foreground) !important;
}

.calendar-event-workshop {
  background-color: var(--color-secondary) !important;
  border-color: var(--color-secondary) !important;
  color: var(--color-secondary-foreground) !important;
}

.calendar-event-planning {
  background-color: var(--color-warning) !important;
  border-color: var(--color-warning) !important;
  color: var(--color-warning-foreground) !important;
}

/* Hiệu ứng hover cho các sự kiện */
.calendar-event-meeting:hover {
  background-color: var(--color-primary-600) !important;
}

.calendar-event-appointment:hover {
  background-color: var(--color-info-600) !important;
}

.calendar-event-deadline:hover {
  background-color: var(--color-error-600) !important;
}

.calendar-event-lunch:hover {
  background-color: var(--color-success-600) !important;
}

.calendar-event-workshop:hover {
  background-color: var(--color-secondary-600) !important;
}

.calendar-event-planning:hover {
  background-color: var(--color-warning-600) !important;
}

/* Initialization styles */
.calendar-container {
  opacity: 0;
  transition:
    opacity 0.3s ease-in-out,
    background-color 0.3s ease,
    color 0.3s ease,
    border-color 0.3s ease;
  will-change: opacity, background-color, color, border-color;
}

.calendar-initialized {
  opacity: 1;
}

/* Fix for initial render issues */
.fc-view-harness {
  min-height: 600px;
  transition: background-color 0.3s ease;
}

/* Theme transition styles */
.theme-transition {
  opacity: 0.9;
  transition:
    opacity 0.3s ease,
    background-color 0.3s ease,
    color 0.3s ease;
}

/* Improve calendar appearance */
.fc-scrollgrid {
  border-radius: var(--radius-md);
  overflow: hidden;
  border: 1px solid var(--fc-border-color) !important;
  width: 100% !important;
  margin: 0 !important;
  padding: 0 !important;
}

/* Remove any default margins/padding that might cause gaps */
.fc-scrollgrid-section-header,
.fc-scrollgrid-section-body {
  margin: 0 !important;
  padding: 0 !important;
}

.fc-scrollgrid-section-header > td,
.fc-scrollgrid-section-body > td {
  margin: 0 !important;
  padding: 0 !important;
}

/* Đảm bảo các phần tử bên trong calendar chiếm toàn bộ chiều rộng */
.fc-view-harness,
.fc-view-harness-active,
.fc-daygrid,
.fc-daygrid-body,
.fc-scrollgrid-sync-table {
  width: 100% !important;
}

/* Đảm bảo bảng calendar chiếm toàn bộ chiều rộng */
.fc table {
  width: 100% !important;
}

.fc-scrollgrid-section-header th {
  padding: 0.75rem 0 !important;
}

.fc-daygrid-day {
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  overflow: hidden !important;
  position: relative;
}

.fc-daygrid-day::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(59, 130, 246, 0.1), transparent);
  transition: left 0.5s ease;
  z-index: 0;
}

.fc-daygrid-day:hover {
  background: linear-gradient(135deg, var(--fc-highlight-color) 0%, rgba(59, 130, 246, 0.05) 100%);
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(59, 130, 246, 0.15);
}

.fc-daygrid-day:hover::before {
  left: 100%;
}

.fc-daygrid-day.fc-day-today {
  background-color: var(--fc-today-bg-color) !important;
  font-weight: bold;
}

/* Ensure events container doesn't overflow */
.fc-daygrid-day-events {
  overflow: hidden !important;
  max-width: 100% !important;
}

.fc-daygrid-event-harness {
  overflow: hidden !important;
  max-width: calc(100% - 2px) !important;
  margin: 0 1px !important;
}

/* Multi-day events styling */
.fc-daygrid-event {
  overflow: hidden !important;
  max-width: 100% !important;
}

.fc-daygrid-event .fc-event-main {
  overflow: hidden !important;
  max-width: 100% !important;
}

/* Specific handling for event content */
.fc-event-main-frame {
  overflow: hidden !important;
  max-width: 100% !important;
}

.fc-event-title-container {
  overflow: hidden !important;
  max-width: 100% !important;
}

/* Responsive event styling */
@media (max-width: 768px) {
  .fc-event {
    font-size: 0.65rem !important;
    padding: 0.15rem 0.25rem !important;
    min-height: 16px !important;
  }

  .fc-event-time {
    font-size: 0.6rem !important;
  }

  .fc-event-title {
    font-size: 0.65rem !important;
  }
}

/* Very small screens */
@media (max-width: 480px) {
  .fc-event {
    font-size: 0.6rem !important;
    padding: 0.1rem 0.2rem !important;
    min-height: 14px !important;
  }

  .fc-event-time {
    font-size: 0.55rem !important;
  }

  .fc-event-title {
    font-size: 0.6rem !important;
  }
}

.fc-daygrid-day-number {
  font-weight: 500;
}

/* Additional button improvements */
.fc-today-button {
  position: relative;
  overflow: hidden;
}

.fc-today-button:after {
  content: '';
  position: absolute;
  top: 50%;
  left: 50%;
  width: 5px;
  height: 5px;
  background: rgba(255, 255, 255, 0.3);
  opacity: 0;
  border-radius: 100%;
  transform: scale(1, 1) translate(-50%, -50%);
  transform-origin: 50% 50%;
}

.fc-today-button:focus:not(:active)::after {
  animation: ripple 0.6s ease-out;
}

@keyframes ripple {
  0% {
    transform: scale(0, 0);
    opacity: 0.5;
  }
  20% {
    transform: scale(25, 25);
    opacity: 0.3;
  }
  100% {
    opacity: 0;
    transform: scale(40, 40);
  }
}

/* Button hover effects */
.fc-button:hover {
  transform: translateY(-1px);
  box-shadow: var(--shadow-md) !important;
}

.fc-button:active {
  transform: translateY(0);
  box-shadow: var(--shadow-sm) !important;
}

/* Improve event styles */
.fc-event {
  transition:
    transform 0.2s ease,
    box-shadow 0.2s ease,
    opacity 0.2s ease !important;
  box-shadow: var(--shadow-sm);
  border-left: 3px solid var(--fc-event-border-color) !important;
}

.fc-event:hover {
  transform: translateY(-1px) scale(1.01);
  box-shadow: var(--shadow-md);
  z-index: 10;
}

/* Responsive adjustments */
@media (max-width: 768px) {
  .calendar-custom-header {
    padding: 0.5rem 0.75rem;
  }

  .fc .fc-toolbar {
    padding: 0.5rem 0.75rem;
    gap: 0.5rem;
  }

  .fc .fc-toolbar-chunk {
    margin-bottom: 0.25rem;
  }

  .fc .fc-toolbar-title {
    font-size: 1.125rem;
  }

  .fc .fc-button {
    padding: 0.3rem 0.6rem;
    font-size: 0.75rem;
    height: 1.875rem;
  }

  .fc .fc-today-button {
    padding: 0.3rem 0.6rem 0.3rem 1.8rem !important;
  }

  .fc .fc-today-button::before {
    left: 0.5rem;
    font-size: 0.8rem;
  }

  .fc .fc-button-group {
    padding: 0;
    gap: 0.3rem;
  }

  .fc .fc-button-group .fc-button {
    padding: 0.3rem 0.5rem;
    min-width: 3.5rem;
    font-size: 0.75rem;
    height: 1.875rem;
  }

  .fc .fc-button-group .fc-button::before {
    font-size: 0.75rem;
    margin-right: 0.25rem;
  }

  .fc .fc-button-group .fc-button-active::after {
    width: 1.25rem;
    height: 0.125rem;
    bottom: 0.15rem;
  }

  .fc .fc-prev-button,
  .fc .fc-next-button {
    width: 1.875rem;
    height: 1.875rem;
  }

  .fc .fc-prev-button .fc-icon,
  .fc .fc-next-button .fc-icon {
    font-size: 0.8125rem;
  }

  .fc-view-harness {
    min-height: 500px;
  }
}

@media (max-width: 640px) {
  .calendar-custom-header {
    padding: 0.5rem;
  }

  .fc .fc-toolbar {
    flex-direction: column;
    align-items: flex-start;
    padding: 0.5rem;
  }

  .fc .fc-toolbar-chunk {
    width: 100%;
    justify-content: space-between;
    margin-bottom: 0.4rem;
  }

  .fc .fc-toolbar-chunk:last-child {
    margin-bottom: 0;
    overflow-x: auto;
    padding-bottom: 0.2rem;
    -ms-overflow-style: none; /* IE and Edge */
    scrollbar-width: none; /* Firefox */
  }

  .fc .fc-toolbar-chunk:last-child::-webkit-scrollbar {
    display: none;
  }

  .fc .fc-toolbar-title {
    font-size: 1rem;
  }

  .fc .fc-button {
    padding: 0.25rem 0.5rem;
    font-size: 0.75rem;
    height: 1.75rem;
  }

  .fc .fc-today-button {
    padding: 0.25rem 0.5rem 0.25rem 1.7rem !important;
    font-size: 0.75rem;
  }

  .fc .fc-today-button::before {
    left: 0.4rem;
    font-size: 0.75rem;
  }

  .fc .fc-button-group {
    padding: 0;
    min-width: 100%;
    justify-content: space-between;
    gap: 0.25rem;
  }

  .fc .fc-button-group .fc-button {
    padding: 0.25rem 0.4rem;
    min-width: auto;
    flex: 1;
    white-space: nowrap;
    font-size: 0.7rem;
    height: 1.75rem;
  }

  .fc .fc-button-group .fc-button-active::after {
    width: 1rem;
    height: 0.1rem;
    bottom: 0.15rem;
  }

  .fc .fc-prev-button,
  .fc .fc-next-button {
    width: 1.75rem;
    height: 1.75rem;
  }

  .fc .fc-prev-button .fc-icon,
  .fc .fc-next-button .fc-icon {
    font-size: 0.8rem;
  }

  .fc-view-harness {
    min-height: 450px;
  }
}

/* Responsive view buttons - show icons only on very small screens */
@media (max-width: 480px) {
  .fc .fc-button-group .fc-button {
    padding: 0.25rem;
    min-width: 2.25rem;
  }

  .fc .fc-button-group .fc-button span {
    display: none;
  }

  .fc .fc-button-group .fc-button::before {
    margin-right: 0;
    font-size: 0.85rem;
  }
}

/* View Select Dropdown */
.calendar-view-select {
  position: relative;
  display: inline-block;
  user-select: none;
}

.calendar-view-select-button {
  display: flex;
  align-items: center;
  justify-content: space-between;
  background-color: var(--color-primary);
  color: var(--color-primary-foreground);
  border: none;
  border-radius: var(--radius-md);
  padding: 0.35rem 0.8rem;
  font-size: 0.8rem;
  font-weight: 500;
  height: 2.125rem;
  min-width: 8rem;
  cursor: pointer;
  box-shadow: var(--shadow-sm);
  transition: all 0.2s ease;
}

.calendar-view-select-button:hover {
  background-color: var(--color-primary-600);
  transform: translateY(-2px);
  box-shadow: var(--shadow-md);
}

.calendar-view-select-icon {
  margin-right: 0.5rem;
  display: inline-flex;
  align-items: center;
  justify-content: center;
}

.calendar-view-select-label {
  flex: 1;
  text-align: left;
  margin-right: 0.5rem;
}

.calendar-view-select-arrow {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  transition: transform 0.2s ease;
  color: var(--color-primary-foreground);
}

.calendar-view-select-button[aria-expanded='true'] .calendar-view-select-arrow {
  transform: rotate(180deg);
}

.calendar-view-select-dropdown {
  position: absolute;
  top: calc(100% + 0.25rem);
  left: 0;
  right: 0;
  background-color: var(--color-background);
  border-radius: var(--radius-md);
  box-shadow: var(--shadow-lg);
  z-index: 100;
  overflow: hidden;
  animation: dropdown-fade 0.2s ease;
}

@keyframes dropdown-fade {
  from {
    opacity: 0;
    transform: translateY(-0.25rem);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.calendar-view-select-option {
  display: flex;
  align-items: center;
  padding: 0.5rem 0.8rem;
  cursor: pointer;
  transition: background-color 0.2s ease;
}

.calendar-view-select-option:hover {
  background-color: var(--color-card-hover);
  color: var(--color-primary);
}

.calendar-view-select-option.selected {
  background-color: var(--color-primary);
  color: var(--color-primary-foreground);
  font-weight: 600;
}

.calendar-view-select-option-icon {
  margin-right: 0.5rem;
  display: inline-flex;
  align-items: center;
  justify-content: center;
}

.calendar-view-select-option-label {
  flex: 1;
}

/* Responsive styles for calendar */
@media (max-width: 768px) {
  .calendar-custom-header {
    padding: 0.5rem 0.75rem;
  }

  .calendar-title {
    font-size: 1.125rem;
  }

  .calendar-nav-button {
    width: 1.875rem;
    height: 1.875rem;
  }

  .calendar-nav-icon {
    font-size: 0.875rem;
  }

  .calendar-today-button {
    padding: 0.3rem 0.6rem;
    font-size: 0.75rem;
    height: 1.875rem;
  }

  .calendar-custom-header-left,
  .calendar-custom-header-right {
    gap: 0.3rem;
  }

  .calendar-view-select-button {
    min-width: 2.5rem; /* Giảm kích thước cho mobile */
    font-size: 0.75rem;
    height: 1.875rem;
    background-color: var(--color-primary) !important;
    color: var(--color-primary-foreground) !important;
  }

  .calendar-add-event-button {
    font-size: 0.75rem;
    height: 1.875rem;
    padding: 0.3rem 0.6rem;
    margin-left: 0.3rem;
  }

  /* Ẩn text trên mobile, chỉ hiện icon */
  .calendar-add-event-text {
    display: none;
  }
}

@media (max-width: 640px) {
  .calendar-custom-header {
    padding: 0.5rem;
  }

  .calendar-title {
    font-size: 1rem;
  }

  .calendar-nav-button {
    width: 1.75rem;
    height: 1.75rem;
  }

  .calendar-nav-icon {
    font-size: 0.8rem;
  }

  .calendar-today-button {
    padding: 0.25rem 0.5rem;
    font-size: 0.7rem;
    height: 1.75rem;
  }

  .calendar-view-select-button {
    min-width: 6rem;
    font-size: 0.7rem;
    height: 1.75rem;
    padding: 0.25rem 0.6rem;
    background-color: var(--color-primary) !important;
    color: var(--color-primary-foreground) !important;
  }

  .calendar-add-event-button {
    font-size: 0.7rem;
    height: 1.75rem;
    padding: 0.25rem 0.5rem;
    margin-left: 0.25rem;
  }

  .calendar-add-event-icon {
    font-size: 0.9rem;
    margin-right: 0.25rem;
  }
}

@media (max-width: 480px) {
  .calendar-custom-header {
    flex-direction: column;
    gap: 0.5rem;
    align-items: stretch;
  }

  .calendar-custom-header-left {
    order: 2;
    justify-content: space-between;
  }

  .calendar-custom-header-center {
    order: 1;
    text-align: left;
  }

  .calendar-custom-header-right {
    order: 3;
    justify-content: flex-end;
  }

  .calendar-title {
    font-size: 0.9rem;
  }

  .calendar-view-select-button {
    min-width: 5rem;
    padding: 0.25rem 0.5rem;
    background-color: var(--color-primary) !important;
    color: var(--color-primary-foreground) !important;
  }

  .calendar-view-select-dropdown {
    min-width: 8rem;
    left: auto;
    right: 0;
  }

  .calendar-add-event-button {
    margin-left: 0.5rem;
    font-size: 0.7rem;
  }
}

/* Hide scrollbar utility class */
.scrollbar-hide {
  -ms-overflow-style: none; /* IE and Edge */
  scrollbar-width: none; /* Firefox */
}

.scrollbar-hide::-webkit-scrollbar {
  display: none; /* Chrome, Safari and Opera */
}
